# 项目发布工具 (Project Release Tool)

## 项目简介

一个基于Python的图形界面自动化发布工具，支持Vue和.NET项目的打包、压缩和发布流程。

## 功能特性

- 🚀 支持多项目配置管理
- 📦 自动执行打包命令
- 🗜️ 自动压缩打包文件
- 📊 实时日志显示
- ⚙️ 灵活的配置选项
- 🔐 管理员权限自动检查

## 支持的项目类型

- Vue.js 项目
- .NET 项目
- 其他支持命令行打包的项目

## 系统要求

- Windows 操作系统
- Python 3.7+
- 管理员权限

## 安装和使用

### 1. 克隆项目
```bash
git clone <repository-url>
cd ProjectReleaseTool
```

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 运行程序

**方式一：无控制台窗口启动（推荐）**
```bash
# Windows - 完全隐藏控制台窗口
start_hidden.bat

# 或者双击 start_no_console.pyw 文件
```

**方式二：使用Python启动脚本**
```bash
python start.py
```

**方式三：使用批处理脚本（Windows）**
```bash
start.bat
# 或者双击 start.bat 文件
```

**方式四：直接运行**
```bash
python main.py
```

**启动方式说明**:
- **start_hidden.bat**: 完全隐藏所有控制台窗口，提供最佳用户体验
- **start_no_console.pyw**: 使用pythonw.exe启动，不显示控制台
- **start.py**: 显示环境检查信息，适合调试
- 程序需要以管理员身份运行以确保有足够的系统权限

## 快速开始

### 第一次使用
1. **启动程序**：双击运行 `python main.py` 或在命令行中执行
2. **添加项目**：点击"添加项目"按钮，配置你的第一个项目
3. **测试发布**：选择项目后点击"开始发布"，查看日志输出
4. **检查结果**：发布完成后，在压缩存储路径中找到生成的ZIP文件

### 示例配置

#### Vue.js 项目
```
项目名称: 我的Vue应用
项目路径: C:\Projects\my-vue-app
项目类型: Vue
打包命令: npm run build
输出路径: dist
压缩存储路径: C:\Releases
```

#### .NET 项目
```
项目名称: 我的.NET应用
项目路径: C:\Projects\my-dotnet-app
项目类型: NET
打包命令: dotnet publish -c Release
输出路径: bin\Release\net6.0\publish
压缩存储路径: C:\Releases
```

### 验证安装
运行以下命令确保环境正确：
```bash
# 检查Python版本
python --version

# 检查必要模块
python -c "import tkinter, subprocess, json, zipfile; print('所有依赖模块正常')"

# 测试程序启动
python main.py
```

## 使用说明

### 1. 添加项目配置
- 点击"添加项目"按钮
- 填写项目信息：
  - 项目名称
  - 项目路径
  - 项目类型 (Vue/NET/其他)
  - 打包命令
  - 输出路径
  - 压缩存储路径

### 2. 执行发布
- 选择要发布的项目
- 点击"开始发布"按钮
- 查看实时日志输出
- 等待发布完成

### 3. 管理项目
- 编辑已有项目配置
- 删除不需要的项目
- 导入/导出配置文件

## 项目结构

```
ProjectReleaseTool/
├── main.py                 # 主程序入口
├── start.py                # Python启动脚本（跨平台）
├── start.bat               # Windows批处理启动脚本
├── gui/                    # GUI模块
│   ├── __init__.py
│   ├── main_window.py      # 主窗口（项目列表、工具栏）
│   ├── project_config.py   # 项目配置窗口（添加/编辑项目）
│   └── log_window.py       # 日志显示组件（实时日志）
├── core/                   # 核心功能模块
│   ├── __init__.py
│   ├── project_manager.py  # 项目管理器（配置CRUD）
│   ├── build_executor.py   # 打包执行器（命令执行）
│   └── file_compressor.py  # 文件压缩器（ZIP压缩）
├── config/                 # 配置文件目录
│   └── projects.json       # 项目配置存储
├── requirements.txt        # 依赖文件（主要使用标准库）
└── README.md              # 详细说明文档
```

### 文件说明

- **main.py**: 程序主入口，处理管理员权限检查和GUI初始化
- **start.py**: 智能启动脚本，检查环境、依赖和权限
- **start.bat**: Windows专用启动脚本
- **gui/main_window.py**: 主界面，包含项目列表、操作按钮和日志区域
- **gui/project_config.py**: 项目配置对话框，支持添加和编辑项目
- **gui/log_window.py**: 日志显示组件，支持实时更新和保存
- **core/project_manager.py**: 项目配置管理，支持JSON持久化
- **core/build_executor.py**: 构建命令执行器，支持多线程和日志回调
- **core/file_compressor.py**: 文件压缩工具，生成带时间戳的ZIP文件

## 配置文件格式

项目配置存储在 `config/projects.json` 文件中：

```json
{
  "projects": [
    {
      "id": "unique-id",
      "name": "项目名称",
      "path": "项目路径",
      "type": "Vue|NET|Other",
      "build_command": "打包命令",
      "output_path": "输出路径",
      "compress_path": "压缩存储路径",
      "exclude_patterns": [
        "*.config",
        "*.log",
        "node_modules",
        ".env*"
      ],
      "git_enabled": true,
      "git_repo_path": "/path/to/git/repo",
      "git_target_path": "releases/v1.0",
      "git_commit_message": "更新项目构建文件",
      "git_push_enabled": false
    }
  ]
}
```

### 文件过滤规则

`exclude_patterns` 字段支持以下格式的过滤规则：

- **文件扩展名**：`*.log`, `*.tmp`, `*.config`
- **文件名**：`package-lock.json`, `yarn.lock`
- **目录名**：`node_modules`, `.git`, `__pycache__`
- **路径模式**：`logs/*`, `config/*.json`
- **通配符**：`temp*`, `*backup*`

**常用过滤规则示例**：
```
# Vue项目
*.map
*.config.js
node_modules
.env*
dist/*.txt

# .NET项目
*.pdb
*.config
appsettings.Development.json
*.log
bin/Debug

# 通用规则
.git
.DS_Store
Thumbs.db
*.tmp
*.cache
```

### Git集成配置

Git集成功能允许您在打包完成后自动将文件复制到Git仓库并提交。

#### Git配置字段说明：

- **`git_enabled`**: 是否启用Git集成（true/false）
- **`git_repo_path`**: Git仓库的本地路径
- **`git_target_path`**: 在Git仓库中的目标路径
- **`git_commit_message`**: 提交消息模板
- **`git_push_enabled`**: 是否自动推送到远程仓库（true/false）

#### Git工作流程：

1. **文件复制**: 将打包输出复制到Git仓库的指定路径
2. **暂存文件**: 执行 `git add` 命令
3. **提交更改**: 使用配置的消息提交更改
4. **推送远程**: 如果启用，推送到远程仓库

#### 使用场景：

- **自动部署**: 将构建文件自动提交到部署仓库
- **版本管理**: 保存每次构建的历史记录
- **团队协作**: 自动同步构建结果到共享仓库

#### 重要说明：

**Git集成与压缩功能是互斥的**：
- 启用Git集成时，程序会跳过压缩步骤，直接将打包文件复制到Git仓库
- 未启用Git集成时，程序会执行压缩操作，生成ZIP文件
- 推荐使用Git集成进行版本管理，因为Git提供了更好的版本控制和历史追踪

#### 注意事项：

- 确保Git仓库路径存在且有效
- 确保有足够的Git操作权限
- 推送到远程仓库需要配置好认证信息

**重要说明**: Git仓库路径与项目路径是不同的概念，详细说明请参考 [Git路径说明文档](git_path_explanation.md)

### Git工具启动优先级

程序会按以下优先级尝试启动Git工具：

1. **小乌龟（TortoiseGit）** - Windows专用，最直观的Git GUI
   - 启动提交对话框：`TortoiseGitProc.exe /command:commit`
   - 启动日志窗口：`TortoiseGitProc.exe /command:log`

2. **Git自带GUI** - 跨平台Git工具
   - `git gui` - Git提交工具
   - `gitk --all` - Git历史查看器

3. **文件管理器** - 兜底方案
   - Windows: 打开Explorer到目标目录
   - macOS: 打开Finder到目标目录
   - Linux: 使用xdg-open打开文件管理器

**使用建议**: 如果您使用Windows，建议安装TortoiseGit以获得最佳体验。

### 智能文件复制功能

程序支持以下高级文件复制功能：

#### 1. 覆盖模式复制
- **不再全量替换**：保留目标目录中的其他文件
- **逐文件覆盖**：只覆盖同名文件，新增不存在的文件
- **保护现有内容**：不会删除目标目录中的其他项目文件

#### 2. 文件过滤支持
- **排除规则生效**：正确应用配置的文件过滤规则
- **通配符支持**：支持 `*.log`、`node_modules/*`、`wwwroot` 等模式
- **详细日志**：显示被跳过的文件和匹配的规则

#### 3. Vue项目特殊处理
- **assets目录清理**：自动删除目标目录的旧assets文件夹
- **解决文件名冲突**：避免新旧assets文件混合导致的问题
- **确保更新完整**：每次发布都是最新的资源文件

#### 使用示例
```
排除规则配置：
wwwroot
*.log
node_modules/*
.git/*

Vue项目发布流程：
1. 删除目标目录的 assets/ 文件夹
2. 复制新的构建文件（跳过wwwroot等）
3. 启动Git工具让您提交
```

## 详细使用指南

### 界面说明

程序启动后，主界面分为两个主要区域：

1. **项目管理区域**（上半部分）
   - 项目列表：显示所有已配置的项目
   - 工具栏：包含添加、编辑、删除、刷新和发布按钮
   - 状态显示：显示项目路径是否有效

2. **日志显示区域**（下半部分）
   - 实时显示打包和压缩过程的详细日志
   - 支持自动滚动和手动清空
   - 可保存日志到文件

### 操作步骤

#### 1. 添加项目配置
1. 点击"添加项目"按钮
2. 在弹出的配置窗口中填写：
   - **项目名称**：给项目起一个便于识别的名称
   - **项目路径**：选择项目源代码的根目录
   - **项目类型**：选择Vue、NET或Other
   - **打包命令**：输入构建命令（会根据项目类型自动填充默认值）
   - **输出路径**：打包后文件的相对路径（相对于项目路径）
   - **压缩存储路径**：压缩文件的存储目录（可选，默认使用项目路径）
   - **排除文件规则**：配置不需要压缩的文件和目录（支持通配符）
   - **发布模式**：选择压缩模式或Git模式
3. 点击"保存"完成配置

#### 2. 执行发布流程
1. 在项目列表中选择要发布的项目
2. 点击"开始发布"按钮
3. 程序将自动执行以下步骤：
   - **步骤1**: 切换到项目目录并执行打包命令
   - **步骤2**: 根据配置选择执行：
     - **压缩模式**: 压缩打包输出文件，生成带时间戳的压缩包
     - **Git模式**: 复制文件到Git仓库并提交（推荐用于版本管理）
4. 查看日志区域了解执行进度和结果

#### 3. 选择发布模式
在项目配置中选择发布方式：

**压缩模式**：
- 生成带时间戳的ZIP文件
- 支持文件过滤规则
- 适合文件传输和备份

**Git模式（推荐）**：
- 将构建文件复制到您的Git目录
- **智能Git工具启动**：优先启动小乌龟（TortoiseGit），其次Git GUI，最后打开文件管理器
- 让您完全控制提交过程：选择文件、编写消息、决定推送
- 自动禁用压缩存储路径配置（不需要）
- 配置参数：
  - **Git仓库路径**：您的Git工作目录路径
  - **目标路径**：在Git目录中的存储路径（如 `releases/v1.0`）
  - **提交消息模板**：默认提交消息（可在Git工具中修改）

#### 4. 管理项目配置
- **编辑项目**：双击项目或选中后点击"编辑项目"
- **删除项目**：选中项目后点击"删除项目"
- **刷新列表**：点击"刷新"按钮更新项目状态

## 常见问题与解决方案

### Q: 程序无法启动？
**可能原因及解决方案：**
- **Python版本问题**：确保使用Python 3.7或更高版本
- **缺少tkinter**：在某些Linux发行版中需要单独安装python3-tk
- **权限问题**：在Windows上以管理员身份运行
- **依赖问题**：虽然主要使用标准库，但确保Python安装完整

**检查方法：**
```bash
python --version  # 检查Python版本
python -c "import tkinter"  # 检查tkinter是否可用
```

### Q: 打包命令执行失败？
**可能原因及解决方案：**
- **项目路径错误**：确保项目路径存在且正确
- **命令不存在**：确保npm、dotnet等工具已安装并在PATH中
- **依赖未安装**：
  - Vue项目：先运行 `npm install` 或 `yarn install`
  - .NET项目：确保安装了对应版本的.NET SDK
- **权限不足**：确保对项目目录有读写权限

**调试方法：**
1. 在命令行中手动执行打包命令
2. 检查项目依赖是否完整
3. 查看日志区域的详细错误信息

### Q: 压缩文件创建失败？
**可能原因及解决方案：**
- **输出路径不存在**：检查打包是否成功，输出目录是否存在
- **磁盘空间不足**：确保有足够的磁盘空间
- **权限问题**：确保对压缩存储路径有写入权限
- **路径包含特殊字符**：避免使用特殊字符或中文路径

### Q: 界面显示异常或卡死？
**解决方案：**
- 重启程序
- 检查是否有长时间运行的打包命令
- 使用"停止发布"按钮终止当前操作

### Q: 出现编码错误（如'gbk' codec can't decode）？
**原因：**
- Windows系统中某些命令输出使用GBK编码，与程序期望的UTF-8编码不匹配

**解决方案：**
- 程序已内置编码错误处理，会自动替换无法解码的字符
- 如果仍有问题，可以在命令前添加 `chcp 65001 &&` 来设置UTF-8编码
- 例如：`chcp 65001 && npm run build`

### Q: 压缩在打包完成前就开始了？
**原因：**
- 早期版本存在时序问题，压缩可能在打包完成前启动

**解决方案：**
- 程序已修复此问题，现在使用回调机制确保正确的执行顺序：
  1. 启动打包命令（异步执行）
  2. 等待打包完成回调
  3. 只有在打包成功后才开始压缩
- 日志会清楚显示"步骤 1/2: 执行打包命令"和"步骤 2/2: 压缩打包文件"

### Q: 项目配置丢失？
**原因：**
- config/projects.json文件损坏或被删除

**解决方案：**
- 程序会自动创建新的配置文件
- 如有备份，可以恢复projects.json文件

## 高级功能

### 自定义打包命令
支持任何命令行工具，例如：
- `npm run build:prod`
- `yarn build`
- `dotnet publish -c Release -r win-x64`
- `mvn clean package`
- `gradle build`

### 批量操作
虽然当前版本不支持批量发布，但可以通过以下方式提高效率：
1. 为不同环境配置不同的项目（如dev、test、prod）
2. 使用脚本预处理项目配置
3. 利用日志保存功能记录发布历史

### 配置文件管理
项目配置存储在 `config/projects.json`，可以：
- 手动编辑配置文件
- 备份配置文件到版本控制系统
- 在团队间共享配置文件

## 开发计划

### 已完成功能 ✅
- [x] 基础项目结构
- [x] 项目配置管理（增删改查）
- [x] GUI界面设计（主窗口、配置窗口、日志组件）
- [x] 打包命令执行（支持多线程）
- [x] 文件压缩功能（ZIP格式）
- [x] 实时日志显示
- [x] 管理员权限检查
- [x] 项目配置验证
- [x] 错误处理和用户提示
- [x] 日志保存功能
- [x] 编码错误处理（Windows GBK/UTF-8兼容）
- [x] 打包压缩时序控制（确保打包完成后再压缩）
- [x] 文件过滤功能（支持通配符排除不需要的文件）
- [x] Git集成功能（自动复制文件到Git仓库并提交）
- [x] 智能界面控制（Git模式自动禁用压缩配置）
- [x] 隐藏CMD窗口（提供更好的用户体验）
- [x] 项目类型智能切换（自动更新默认配置）
- [x] 交互式Git操作（智能启动小乌龟/Git GUI/文件管理器）
- [x] 智能文件复制（覆盖模式、文件过滤、Vue项目assets清理）

### 计划中功能 📋
- [ ] 版本管理功能（Git集成）
- [ ] 批量发布功能
- [ ] 发布历史记录
- [ ] 配置文件导入/导出
- [ ] 主题切换（深色/浅色模式）
- [ ] 多语言支持
- [ ] 插件系统
- [ ] 远程部署功能
- [ ] 构建缓存优化
- [ ] 邮件通知功能

### 技术改进 🔧
- [ ] 单元测试覆盖
- [ ] 性能优化
- [ ] 内存使用优化
- [ ] 异常处理完善
- [ ] 日志系统改进
- [ ] 配置文件加密

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

MIT License